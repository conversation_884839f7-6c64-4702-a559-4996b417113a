#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}


.dashboard {
  font-family: 'Arial', sans-serif;
  background-color: #f4f7fc;
  padding: 20px;
}

.dashboard-header {
  text-align: center;
  margin-bottom: 20px;
}

.dashboard-header h1 {
  font-size: 32px;
  color: #333;
  font-weight: bold;
}

.dashboard-content {
  display: flex;
  justify-content: space-between;
  gap: 20px;
  flex-wrap: wrap;
}

.dashboard-left,
.dashboard-right {
  flex: 1;
  min-width: 300px;
}

.dashboard-left {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-right {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}


/* expenseList */
.expense-list {
  color: black;
  margin: 5px;
}

h2 {
  text-align: center;
  font-size: 20px;
}

.expense-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.expense-table th,
.expense-table td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

.expense-table th {
  background-color: #f4f4f4;
}

.edit-btn,
.delete-btn {
  padding: 6px 12px;
  margin-right: 5px;
  border: none;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.edit-btn:hover,
.delete-btn:hover {
  background-color: #0056b3;
}

.delete-btn {
  background-color: #dc3545;
}

.delete-btn:hover {
  background-color: #c82333;
}



/* ExpenseForm */
.expense-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
  background-color: #ffffff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  margin: 20px auto;
}

.expense-form input,
.expense-form textarea {
  padding: 10px;
  border-radius: 5px;
  border: 1px solid #ddd;
  font-size: 16px;
}

.expense-form button {
  padding: 10px;
  background-color: #007BFF;
  color: white;
  font-size: 16px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.expense-form button:hover {
  background-color: #0056b3;
}


.auth-page {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5rem;
  height: 100vh;
  padding: 10px;
}

@media (max-width: 768px) {
  .auth-page {
  margin-top: 50vw;
    flex-direction: column;
  }
}
