const express = require("express");
const { addExpense, getExpenses, updateExpense, deleteExpense } = require("../controllers/expenseController");
const protect = require("../middlewares/authMiddleware");
const router = express.Router();

router.post("/", protect, addExpense);
router.get("/", protect, getExpenses);
router.put("/:id", protect, updateExpense);
router.delete("/:id", protect, deleteExpense);

module.exports = router;
